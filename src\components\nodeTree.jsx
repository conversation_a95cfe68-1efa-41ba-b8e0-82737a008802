import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from '../index.module.scss';
import { Input, Tree } from 'antd';
import { ContainerOutlined } from '@ant-design/icons';

const NodeTree = ({_treeData}) => {
  return (
    <Tree
      defaultExpandedKeys={[0,1,2,3]}
      showIcon={true}
      showLine={true}
      treeData={_treeData}
      onSelect={(v,obj)=>{
        props.onChange(obj.node.title)
      }}
    />
  );
};

export default NodeTree;
