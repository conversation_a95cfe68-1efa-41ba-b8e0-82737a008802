
const apiUrl = 'http://localhost:30010/api';
// const apiUrl = 'http://*************:5000/api';

const APIS = {
  saveMarkers: (params) => doRequest(params, 'POST', apiUrl+'/saveMarkers'), //保存，新增标记
  updateMarkers: (params) => doRequest(params, 'PUT', apiUrl+'/markers'), //编辑标记
  getMarkers: (params) => doRequest(params, 'GET', apiUrl+'/markers'), //获取标记
  rmMarkers: (params) => doRequest(params, 'DELETE', apiUrl+'/markers'), //删除标记
  addProjects: (params) => doRequest(params, 'POST', apiUrl+'/nodes'), //新增项目
  getAllProjects: (params) => doRequest(params, 'GET', apiUrl+'/nodes'), //树
  rmProjects: (params) => doRequest(params, 'DELETE', apiUrl+'/nodes'), //删除项目
  addAccounts: (params) => doRequest(params, 'POST', apiUrl+'/users'), //新增账户
  getAccountList: (params) => doRequest(params, 'GET', apiUrl+'/users'), //获取账户数量
  rmAccount: (params) => doRequest(params, 'DELETE', apiUrl+'/users'), //删除账户
  editAccount: (params) => doRequest(params, 'PUT', apiUrl+'/users'), //编辑账户
  checkUser:(params)=> doRequest(params, 'POST', apiUrl+'/login'), //判断用户密码
};


const doRequest = async (params, method, url) => {
 try {
    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      ...(method === 'GET' ? {} : { body: JSON.stringify({ 
        ...params,
        operatorId: JSON.parse(localStorage.getItem('_USER'))?.id,
       }) }),
    });

    if (!response.ok) {
      throw new Error('报错！');
    }

    const data = await response.json();
    return data; // 返回数据
  } catch (error) {
    console.error('Error:', error);
    throw error; // 抛出错误以便调用方处理
  }
};

export default APIS;