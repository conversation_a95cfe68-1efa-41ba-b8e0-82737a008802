{"name": "map-marker-electron", "version": "1.0.0", "main": "main.js", "homepage": "./", "scripts": {"build-backend": "pyinstaller --onefile src/backend/sql.py --distpath dist --name sql", "start": "react-scripts start", "build": "react-scripts build", "electron": "electron .", "dev": "concurrently \"cross-env BROWSER=none npm start\" \"wait-on http://localhost:3000 && electron .\"", "package": "electron-builder --dir", "dist": "npm run build-backend && electron-builder --win --ia32"}, "build": {"appId": "com.example.mapmarker", "productName": "警力资源管理", "extends": null, "files": ["build/**/*", "main.js", "public/icons/*.ico", "dist/sql.exe"], "extraFiles": [{"from": "dist/sql.exe", "to": "resources/sql.exe"}], "extraMetadata": {"main": "main.js"}, "win": {"target": "nsis", "icon": "public/icons/police.ico"}, "mac": {"target": "dmg", "icon": "public/icons/police.icns"}, "linux": {"target": "AppImage", "icon": "public/icons/police.png"}}, "dependencies": {"@ant-design/icons": "^5.6.1", "antd": "^5.24.6", "axios": "^1.7.0", "coordtransform": "^2.1.2", "file-saver": "^2.0.5", "leaflet": "^1.9.4", "leaflet.chinatmsproviders": "^3.0.6", "react": "^18.3.0", "react-dom": "^18.3.0", "react-leaflet": "^4.2.1", "react-router-dom": "^7.4.1", "react-scripts": "^5.0.1", "web-vitals": "^4.2.4"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^13.6.9", "electron-builder": "^24.13.3", "sass": "^1.86.1", "wait-on": "^7.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}