# Map-Marker-App

## 开发环境

* Node.js 22.14.0
* python 3.13.2

## 调试方法

软件分为前端和后端，其中后端采用python实现，代码在`src/backend/sql.py`中，主要基于flask和SQLAlchemy实现。

👍SQLAlchemy 是 Python 和数据库之间的桥梁，该库可以用 Python 代码操作数据库，而不用手写 SQL（当然你也可以写）。

运行`sql.py`后将会在后台启动一个进程用于接收数据并存储到数据库中

```shell
# 安装 requirements.txt中指定的python包
pip install -r requirements.txt
# 启动后端
python src/backend/sql.py
```

可以通过以下命令调试或者打包app:

```shell
npm install   # 安装 package.json 中指定的Node.js包
npm start     # web浏览器中启动前端 (需要手动启动sql.py)
npm run dev   # 启动app（前端+后端，系统自动启动sql.py）
```

注意😲：利用`npm start`调试时需要手动启动`sql.py`，因为npm start只启动前端，但是利用`npm run dev`调试时，系统将自动运行该文件，无需手动启动

## 打包APP

可以通过以下命令打包app:

```shell
npm run dist  # 打包app，安装包会在dist目录下
```

提示🙂：`npm run dist`将自动通过`pyinstaller`把`sql.py`打包为一个可执行文件，并在app运行时自动启动

---

## Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

### Available Scripts

In the project directory, you can run:

#### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

#### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

#### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

#### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

### Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

#### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

#### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

#### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

#### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

#### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

#### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
