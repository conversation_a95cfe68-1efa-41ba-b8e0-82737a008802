import React, { useEffect, useState } from 'react';
import './Login.css';
import APIS from './services';
import { message } from 'antd';

/**
 * 登录组件
 * @param {Object} props - 组件属性
 * @param {Function} props.onLogin - 登录成功后的回调函数，用于跳转到主页面
 */
function Login({ onLogin }) {
  // 用户名状态变量
  const [username, setUsername] = useState('');
  // 密码状态变量
  const [password, setPassword] = useState('');

  /**
   * 处理登录表单提交
   * @param {Event} e - 表单提交事件
   */
  const handleSubmit = async(e) => {
    // 阻止表单默认提交行为
    e.preventDefault();

    // 调用API验证用户登录信息
    const res = await APIS.checkUser({
      userInput:username,
      password:password,
    });

    // 处理登录结果
    if(res.error){
      // 登录失败，显示错误信息
      message.error(res.error)
    }else{
      // 登录成功，将用户信息保存到本地存储
      localStorage.setItem('_USER',JSON.stringify(res));
      // 调用父组件的 onLogin 方法跳转到地图页面
      onLogin();
    }
  };

  // 渲染登录界面
  return (
    <div className="login-container">
      <div className="login-box">
        <h2>登录</h2>
        {/* 登录表单 */}
        <form onSubmit={handleSubmit}>
          {/* 用户名输入框 */}
          <div className="form-group">
            <label htmlFor="username">用户名</label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="请输入用户名"
              required
            />
          </div>
          {/* 密码输入框 */}
          <div className="form-group">
            <label htmlFor="password">密码</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入密码"
              required
            />
          </div>
          {/* 登录按钮 */}
          <div className="buttons">
            <button type="submit">登录</button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 导出Login组件作为默认导出
export default Login;