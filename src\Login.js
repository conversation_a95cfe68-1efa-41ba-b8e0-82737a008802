import React, { useEffect, useState } from 'react';
import './Login.css';
import APIS from './services';
import { message } from 'antd';

function Login({ onLogin }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async(e) => {
    e.preventDefault();
    const res = await APIS.checkUser({
      userInput:username,
      password:password,
    });
    if(res.error){
      message.error(res.error)
    }else{
      localStorage.setItem('_USER',JSON.stringify(res));
      onLogin(); // 登录成功，调用父组件的 onLogin 方法跳转到地图页面
    }
  };

  return (
    <div className="login-container">
      <div className="login-box">
        <h2>登录</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username">用户名</label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="请输入用户名"
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="password">密码</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入密码"
              required
            />
          </div>
          <div class="buttons">
            <button type="submit">登录</button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Login;