import React, { useState, useEffect, useRef, use } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ine, Polygon, useMapEvents, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import "leaflet.chinatmsproviders";
import './App.css';
import coordtransform from 'coordtransform';
import { Button, Avatar, Dropdown, Tag, Select, Input, Switch, message, Divider } from 'antd';
import Account from './components/account';
import SideMenu from './components/sideMenu';
import Password from './components/password';
import ProjectManage from './components/projectManage';
import styles from './index.module.scss';
import { SyncOutlined, CopyOutlined, ProfileOutlined, FolderOpenOutlined, HolderOutlined, SwapOutlined, UserOutlined, LogoutOutlined, KeyOutlined, EnvironmentOutlined, DeleteOutlined, CloseOutlined, ContainerOutlined, SearchOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import APIS from './services';

// 修复 Leaflet 默认图标问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

function MapPage({ logOut }) {
  const [center, setCenter] = useState([33.7416, 112.9033])
  const [markers, setMarkers] = useState([]);
  const [polyline, setPolyline] = useState([]);
  const [stats, setStats] = useState({
    longitude: 112.9033,
    latitude: 33.7416,
    count: 0,
  });

  const [mapLayer, setMapLayer] = useState('street');
  const [title, setTitle] = useState('治安动态')
  const [contextMenu, setContextMenu] = useState(null);
  const [accountDialogVisable, setAccountDialogVisable] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [showMarkerList, setShowMarkerList] = useState(false);
  const [projectVisible, setProjectVisible] = useState(false);
  const [eidtMarker, setEidtMarker] = useState({});
  const [_treeData, setTreeData] = useState([]);

  const [inputVal, setInputVal] = useState('');
  const [userInfo, setUserInfo] = useState({});

  // 使用 useRef 保存 Marker 实例和地图实例
  const markerRefs = useRef([]);
  const mapRef = useRef(null); // 用于存储地图实例

  const tiandituTk = 'd91409abb86f6468d7e8c42447675a25';

  const worldBounds = L.latLngBounds(L.latLng(-90, -180), L.latLng(90, 180));

  const maskPolygon = [
    worldBounds.getSouthWest(),
    worldBounds.getNorthWest(),
    worldBounds.getNorthEast(),
    worldBounds.getSouthEast(),
    worldBounds.getSouthWest()
  ];


  // 初始化模拟数据
  useEffect(() => {
    const mockPolyline = [
      [33.7671, 112.9038],
      [33.7589, 112.9032],
      [33.7587, 112.9023],
      [33.7488, 112.9043],
      [33.7349, 112.9075],
      [33.7296, 112.9094],
      [33.7165, 112.9112],
      [33.7166, 112.8932],
      [33.7212, 112.8880],
      [33.7263, 112.8880],
      [33.7283, 112.8824],
      [33.7323, 112.8807],
      [33.7330, 112.8777],
      [33.7353, 112.8756],
      // [33.7359, 112.8785],
      [33.7360, 112.8759],
      [33.7368, 112.8762],
      [33.7380, 112.8768],
      [33.7392, 112.8776],
      [33.7403, 112.8784],
      [33.7412, 112.8791],
      [33.7418, 112.8798],
      [33.7425, 112.8806],
      [33.7430, 112.8814],
      [33.7436, 112.8825],
      [33.7442, 112.8835],
      // [33.7441, 112.8829],
      [33.7417, 112.8718],  
      [33.7422, 112.8713],
      [33.7426, 112.8712],
      // [33.7427, 112.8715],
      // [33.7452, 112.8690],
      // [33.7463, 112.8666],
      // [33.7481, 112.8671],
      // [33.7510, 112.8712],
      [33.7447, 112.8690],
      [33.7450, 112.8678],
      [33.7454, 112.8669],
      [33.7460, 112.8661],
      [33.7467, 112.8659],
      [33.7477, 112.8660],
      [33.7483, 112.8670],
      [33.7522, 112.8710],
      [33.7549, 112.8775],
      [33.7602, 112.8822],
      [33.7624, 112.8877],
      [33.7663, 112.8912],
      [33.7671, 112.9038]
    ];
    init();
    setPolyline(mockPolyline);
    setUserInfo(JSON.parse(localStorage.getItem('_USER')))
  }, []);

  const init = async () => {
    getTreeData();
    const data = await APIS.getMarkers();
    setMarkers(data);
    setStats((prev) => ({ ...prev, count: data.length }));
  }


  // 获取树
  const getTreeData = async () => {
    const data = await APIS.getAllProjects();
    let _tmp = [{ ...data }]
    _tmp[0].children.map(item => {
      item.icon = () => (<div style={{ background: item.color }} className={styles['dot']} />)
      item.children.map(i => {
        i.icon = <ContainerOutlined />
        return i
      })
      return item
    })
    setTreeData(_tmp)
  }

  // 切换图层
  const toggleMapLayer = () => {
    setMapLayer((prev) => {
      if (prev === 'street') {
        // 从 GCJ-02 转换为 WGS-84
        const [lat, lng] = gcj02ToWgs84(stats.latitude, stats.longitude);
        setStats((prevStats) => ({ ...prevStats, latitude: lat, longitude: lng }));
        return 'satellite';
      } else {
        // 从 WGS-84 转换回 GCJ-02
        const [lat, lng] = coordtransform.wgs84togcj02(stats.longitude, stats.latitude);
        setStats((prevStats) => ({ ...prevStats, latitude: lat, longitude: lng }));
        return 'street';
      }
    });
  }


  // 重置地图到初始位置
  const resetMap = () => {
    mapRef.current.setView(center, 14); // 重置到初始中心点和缩放级别
    message.success('地图已刷新！')
  };

  // 鼠标事件监听组件
  const MouseTracker = () => {
    useMapEvents({
      mousemove(e) {
        setStats((prev) => ({
          ...prev,
          latitude: e.latlng.lat.toFixed(4),
          longitude: e.latlng.lng.toFixed(4),
        }));
      },
      contextmenu(e) {
        setContextMenu({
          position: [e.latlng.lat, e.latlng.lng],
          x: e.containerPoint.x,
          y: e.containerPoint.y,
          isMarker: false,
        });
      },
    });
    return null;
  };

  // 处理标记拖动结束
  const handleMarkerDragEnd = (index) => (event) => {
    const newPosition = event.target.getLatLng();
    const updatedMarkers = [...markers];
    updatedMarkers[index].position = [newPosition.lat, newPosition.lng];
    setMarkers(updatedMarkers);
  };

  // 处理标记右键菜单
  const handleMarkerContextMenu = (item, index, event) => {
    event.originalEvent.preventDefault();
    setContextMenu({
      x: event.containerPoint.x,
      y: event.containerPoint.y,
      isMarker: true,
      markerIndex: index,
      markerId: item.id,
      marker: item
    });
    setEidtMarker(markers[index])
  };

  // 编辑标记字段
  const handleChangeInfo = (newType, name) => {
    setEidtMarker({
      ...eidtMarker,
      [name]: newType,
      ...(name === 'type' ? { secType: '' } : {})
    })
  }

  //编辑或新增标记
  const confirmMenu = async () => {
    const isEdit = contextMenu.isMarker
    const res = await APIS[isEdit ? 'updateMarkers' : 'saveMarkers']({
      markers: [{
        ...eidtMarker,
        ...(isEdit ? {} : {
          position: contextMenu.position,
        }),
      }],
    })
    if (!res.error) {
      message.success(isEdit ? '编辑标记成功！' : '新增标记成功！');
      //重新获取markers列表
      markerManage()
      setContextMenu(null);
      setEidtMarker({});
    } else {
      message.error(res.error);
    }

  }

  // 删除标记
  const handleDeleteMarker = async () => {
    if (contextMenu && contextMenu.isMarker) {
      const res = await APIS.rmMarkers({
        marker: contextMenu.markerId,
        marker: contextMenu.marker,
      })
      if (!res.error) {
        message.success('删除标记成功！');
        //重新获取markers列表
        markerManage()
        setStats((prev) => ({ ...prev, count: prev.count - 1 }));
        setContextMenu(null);
        setEidtMarker({});
      } else {
        message.error(res.error);
      }
    }
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(null);
    setEidtMarker({})
  };

  // 坐标转换函数：GCJ-02 转 WGS-84
  const gcj02ToWgs84 = (lat, lng) => {
    const [newLng, newLat] = coordtransform.gcj02towgs84(lng, lat);
    return [newLat, newLng];
  };

  const handleAccountDialog = () => {
    setAccountDialogVisable(!accountDialogVisable)
  }

  const handleChange = () => {
    setEditModal(v => !v)
  }

  //获取标记列表
  const markerManage = async (v) => {
    const data = await APIS.getMarkers();
    setMarkers(data);
    v == 'hasModal' && setShowMarkerList(v => !v)
  }

  const chooseMarker = (postion, idx) => {
    mapRef.current.flyTo(postion, 17); // 重置到初始中心点和缩放级别
    markerRefs?.current[idx]?._popup?.setLatLng(postion);
    markerRefs?.current[idx]?._popup?.openOn(mapRef.current)

  }

  const saveMarkers = () => {
    // 先保存到 localStorage
    localStorage.setItem('markers', JSON.stringify(markers));

    // 创建要保存的数据对象
    const dataToSave = {
      markers: markers,
      treeData: _treeData
    };

    // 将数据转换为 JSON 字符串
    const jsonString = JSON.stringify(dataToSave, null, 2);

    // 创建 Blob 对象
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' });

    // 使用 file-saver 保存文件
    // 文件名包含时间戳以避免覆盖
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    saveAs(blob, `map-data-${timestamp}.json`);
  };

  const hexToFeColorMatrix = (hexColor) => {
    // 检查十六进制颜色格式是否正确
    if (!/^#[0-9A-Fa-f]{6}$/.test(hexColor)) {
      throw new Error("Invalid hex color format. Expected #RRGGBB.");
    }

    // 去掉 "#" 前缀，提取 RGB 值
    hexColor = hexColor.slice(1);

    // 将十六进制颜色转换为 RGB 值, 滤镜使用
    const r = parseInt(hexColor.substring(0, 2), 16) / 255;
    const g = parseInt(hexColor.substring(2, 4), 16) / 255;
    const b = parseInt(hexColor.substring(4, 6), 16) / 255;

    // 构建 feColorMatrix 的 values 属性字符串
    const matrixValues = `
        0 0 0 0 ${r}
        0 0 0 0 ${g}
        0 0 0 0 ${b}
        0 0 0 1 0
    `.trim();
    return matrixValues;
  }

  function darkenHexColor(hex, amount) {
    // 去掉 # 符号
    hex = hex.replace('#', '');

    // 将十六进制转换为 RGB
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);

    // 调整 RGB 值（减少每个通道的值）
    r = Math.max(0, r - amount);
    g = Math.max(0, g - amount);
    b = Math.max(0, b - amount);

    // 转换回十六进制并返回
    let newHex = '#' + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1).toUpperCase();

    return newHex;
  }

  function lightenColor(hex, percent) {
    // 移除 # 号（如果有）
    hex = hex.replace('#', '');
    
    // 解析 RGB 值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    // 混合白色
    const lighten = (channel) => Math.round(channel + (255 - channel) * (percent / 100));
    
    const newR = lighten(r);
    const newG = lighten(g);
    const newB = lighten(b);
    
    // 转回十六进制
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

  return (
    <div className={styles['page-container']}>
      <div className={styles['header-container']}>
        <div id="header-container" className={styles['header-text']}>
          <div className={styles['btn-style']}>
            <Button className={styles['hover-button']} type='text' onClick={() => saveMarkers()}>
              <CopyOutlined />
              保存标记</Button>
            <Button className={styles['hover-button']} type='text' onClick={() => markerManage('hasModal')}>
              <HolderOutlined />
              标记管理</Button>
            <Button className={styles['hover-button']} type='text' onClick={() => setProjectVisible(true)}>
              <ProfileOutlined />
              项目管理</Button>
          </div>
          <div className={styles['header-text-container']}>
            {`鲁阳派出所${title}感知图`} </div>
          <div className={styles['btn-style']} style={{ display: 'flex', alignItems: 'center' }}>
            <Button className={styles['hover-button']} type='text' onClick={() => handleAccountDialog()}>
              <FolderOpenOutlined />
              账号管理</Button>
            <Button className={styles['hover-button']} type='text' onClick={resetMap}>
              <SyncOutlined />
              刷新地图</Button>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button className={styles['hover-button']} type='text' onClick={toggleMapLayer}>
                <SwapOutlined />
                {mapLayer === 'street' ? '切换卫星图' : '切换街道图'}
              </Button>
              <Divider type="vertical" style={{ borderColor: '#cecece' }} />
              <Dropdown dropdownRender={() => {
                return (
                  <div className={styles['user-info']}>
                    <div>用户：{userInfo.name ?? '--'}</div>
                    <div style={{ borderBottom: '1.5px solid rgb(218 236 254)', paddingBottom: 5, marginBottom: 5 }}>警号：{userInfo.policeId ?? '--'}</div>
                    <div className={styles['menu-icon']} onClick={() => handleChange()}><KeyOutlined style={{ fontSize: 14 }} /> 修改密码</div>
                    <div className={styles['menu-icon']} onClick={logOut}><LogoutOutlined style={{ fontSize: 14 }} /> 退出登录</div>
                  </div>
                )
              }}>

                <div className={styles['avator-bg']}>
                  <Avatar style={{ background: 'rgb(190 196 201)' }} size={28} icon={<UserOutlined />} />
                </div>
              </Dropdown>
            </div>
          </div>
        </div>
      </div>

      <div className={styles['main-container']}>
        <div className="map-container">
          <MapContainer
            center={center}
            zoom={14}
            ref={mapRef}
            style={{ height: '100%', width: '100%' }}
            attributionControl={false} // 禁用 attributionControl
            zoomControl={false}
          >
            {mapLayer === 'street' && (
              <>
                {/* 矢量地图层 */}
                <TileLayer
                  // url={`http://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituTk}`}
                  url='https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  subdomains={['0', '1', '2', '3', '4', '5', '6', '7']}
                />
                {/* 标注层 */}
                <TileLayer
                  url={`http://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituTk}`}
                  attribution='Tiles &copy; Esri &mdash; Source: Esri, Maxar, Earthstar Geographics"'
                  subdomains={['0', '1', '2', '3', '4', '5', '6', '7']}
                />
              </>
            )}
            {mapLayer === 'satellite' && (
              <>
                {/* 卫星地图层 */}
                <TileLayer
                  url="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                />
                {/* 标注层 */}
                <TileLayer
                  url={`http://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituTk}`}
                  subdomains={['0', '1', '2', '3', '4', '5', '6', '7']}
                />
              </>
            )}

            {markers.map((marker, index) => (
              <Marker
                key={index}
                position={marker.position}
                icon={
                  L.divIcon({
                    html: `
                    <svg t="1749024774843" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="47643" width="32" height="32">
                    <path d="M512 0l424.96 89.4464-424.96 89.3952-424.96-89.3952z" fill="${lightenColor(marker.color, 80)}" p-id="47644"></path>
                    <path d="M512 1024V178.8416l424.96-89.2928z" fill="${lightenColor(marker.color, 55)}" p-id="47645"></path>
                    <path d="M512 1024V179.2L87.04 89.8048z" fill="${lightenColor(marker.color, 20)}" p-id="47646"></path></svg>
                      `,
                    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                    className: '',
                    iconSize: [50, 40],
                    iconAnchor: [18, 32],
                    popupAnchor: [-2, -32],
                    shadowSize: [40, 40],
                  })
                }
                draggable={true}
                ref={(ref) => (markerRefs.current[index] = ref)}
                eventHandlers={{
                  dragend: handleMarkerDragEnd(index),
                  contextmenu: (event) => handleMarkerContextMenu(marker, index, event),
                }}
              >
                <Popup>{marker.name}</Popup>
              </Marker>

            ))}

            {/* <Polyline positions={polyline} color="red" /> */}
            <Polygon
              positions={polyline}
              color="#ace4f8"
              fillColor="#fff"
              fillOpacity={0}
              weight={5}
            />
            {/* 蒙层（使用反向多边形） */}
            <Polygon
              positions={[maskPolygon, polyline]}
              color="#000"
              fillColor="rgb(17 34 55)"
              fillOpacity={0.7}
              weight={0}
              interactive={false}
            />
            <MouseTracker />
          </MapContainer>
          {showMarkerList && <div className={styles['marker-menu']}>
            <div className={styles['marker-menu-header']}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {/* <span className={styles['divide-line']} /> */}
                标记<span style={{
                  fontSize: 11, color: '#A8D6FF'
                }}>（支持点击定位）</span></div>
            </div>
            <div className={styles['marker-search']}>
              <Input allowClear addonBefore={<SearchOutlined style={{ color: '#01c2ff' }} />} style={{ margin: '5px 5px 5px 0' }} size='small' onChange={(v) => {
                setInputVal(v.target.value)
              }} />
              <CloseOutlined style={{ color: '#01c2ff', fontSize: 14 }} onClick={() => markerManage('hasModal')} />
            </div>
            <div className={styles['marker-menu-content']}>
              {markers?.map((item, idx) => {
                if (!item.name.includes(inputVal)) return
                const _parent = _treeData?.[0].children?.find(v => v.key === item.type)
                return (
                  <div className={styles['marker-item']} onClick={() => chooseMarker(item.position, idx)}>
                    <div className={styles['marker-item-event']}><EnvironmentOutlined style={{ color: '#01c2ff' }} /> {item.name}</div>
                    <div>
                      <Tag className={styles['marker-item-tag']} color={item.color}>
                        {_parent.title}
                      </Tag>
                      <Tag className={styles['marker-item-second-tag']}>
                        {_parent?.children?.find(v => v.key === item.secType).title}
                      </Tag>
                    </div>
                  </div>
                )
              })}
              {markers?.map((item, idx) => {
                if (!item.name.includes(inputVal)) return
                const _parent = _treeData?.[0].children?.find(v => v.key === item.type)
                return (
                  <div className={styles['marker-item']} onClick={() => chooseMarker(item.position, idx)}>
                    <div className={styles['marker-item-event']}><EnvironmentOutlined style={{ color: '#01c2ff' }} /> {item.name}</div>
                    <div>
                      <Tag className={styles['marker-item-tag']} color={item.color}>
                        {_parent.title}
                      </Tag>
                      <Tag className={styles['marker-item-second-tag']}>
                        {_parent?.children?.find(v => v.key === item.secType).title}
                      </Tag>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>}

          {/* 右键菜单 */}
          {contextMenu && (
            <div
              className={styles['context-menu']}
              style={{
                top: contextMenu.y - 215,
                left: contextMenu.x - 123,
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>{`${contextMenu.isMarker ? '编辑' : '添加'}标记`}</div>
                {contextMenu.isMarker && <DeleteOutlined style={{ color: '#2196f3' }} onClick={handleDeleteMarker} />}
              </div>
              <div style={{ fontSize: 12, color: '#A8D6FF' }}>
                一级项目：
                <Select
                  style={{ margin: '10px 0', width: 150 }}
                  size='small'
                  onChange={(v) => handleChangeInfo(v, 'type')}
                  value={eidtMarker?.type}
                >
                  {_treeData[0]?.children?.map((item) => (
                    <option key={item.key} value={item.key}>
                      {item.title}
                    </option>
                  ))}
                </Select>
              </div>
              <div style={{ fontSize: 12, color: '#A8D6FF' }}>
                二级项目：
                <Select
                  style={{ marginBottom: 10, width: 150 }}
                  size='small'
                  onChange={(v) => handleChangeInfo(v, 'secType')}
                  value={eidtMarker?.secType}
                >
                  {_treeData[0]?.children?.find(v => (v.key === eidtMarker?.type || v.title === eidtMarker?.type))?.children.map((item) => (
                    <option key={item.key} value={item.key}>
                      {item.title}
                    </option>
                  ))}
                </Select>
              </div>
              <div style={{ fontSize: 12, color: '#A8D6FF' }}>
                事件名称：
                <Input style={{ width: 150 }} size='small' value={eidtMarker?.name} onChange={v => handleChangeInfo(v.target.value, 'name')} />
              </div>
              <div style={{ display: 'flex', marginTop: 12 }}>
                <Button className={styles['add-marker-btn']} style={{ marginRight: 8 }} type='default' onClick={() => closeContextMenu()}>取消</Button>
                <Button className={styles['add-marker-btn']} type='primary' onClick={() => confirmMenu()}>确认</Button>
              </div>
            </div>
          )}
        </div>
        {_treeData.length && <SideMenu
          markers={markers}
          _treeData={_treeData}
          onFilter={async (v) => {
            const data = await APIS.getMarkers();
            if (v.title === '全部') {
              setMarkers(data);
            } else {
              const _tmp = data.filter(i => i.type === v.key || i.secType === v.key)
              setMarkers(_tmp)
            }
            setTitle(v.title === '全部' ? '' : v.title)
          }} stats={stats} changeSelect={(obj) => console.log(obj)} />}
      </div>
      <Account visable={accountDialogVisable} handleCancel={() => handleAccountDialog()} />
      <Password visible={editModal} handleCancel={() => handleChange()} userInfo={userInfo} />
      <ProjectManage
        _treeData={_treeData}
        changeTree={v => setTreeData([...v])}
        visible={projectVisible}
        handleCancel={() => setProjectVisible(false)}
        onRefresh={() => getTreeData()}
      />
    </div>
  );
}

export default MapPage;