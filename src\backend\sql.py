import os
import json
import hashlib
import psutil
import wmi
import socket
import uuid
import platform
import requests

from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

from werkzeug.security import generate_password_hash, check_password_hash
from cryptography.fernet import Fernet

app = Flask(__name__)
finger_server_url = 'http://47.117.176.90:30030'

# 配置 SQLite 数据库
# 创建数据库表
# 获取用户目录（例如 C:\Users\<USER>\Users\<Username>\AppData\Local\PoliceMapMarker\map_data.db
app_data_dir = os.path.join(user_home, "AppData", "Local", "PoliceMapMarker")
database_path = os.path.join(app_data_dir, "map_data.db")
# 确保目录存在
if not os.path.exists(app_data_dir):
  os.makedirs(app_data_dir)
# 配置 SQLite 数据库路径
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

class User(db.Model):
  __tablename__ = 'user'
  id = db.Column(db.Integer, primary_key=True)
  name = db.Column(db.String(100), nullable=False)
  policeId = db.Column(db.String(20), unique=True, nullable=False)
  password_hash = db.Column(db.String(128), nullable=False)
  privilege = db.Column(db.String, nullable=False, default='viewer')
  created_at = db.Column(db.DateTime, default=datetime.utcnow)

  def set_password(self, password):
    self.password_hash = generate_password_hash(password)

  def check_password(self, password):
    return check_password_hash(self.password_hash, password)

class Marker(db.Model):
  __tablename__ = 'marker'
  id = db.Column(db.Integer, primary_key=True)
  name = db.Column(db.String(100), nullable=False)
  typeId = db.Column(db.Integer, nullable=False)
  secTypeId = db.Column(db.Integer)
  latitude = db.Column(db.Float, nullable=False)
  longitude = db.Column(db.Float, nullable=False)
  color = db.Column(db.String(20))
  created_at = db.Column(db.DateTime, default=datetime.utcnow)

  parentId = db.Column(db.Integer, db.ForeignKey('projectNode.id', ondelete='CASCADE'), nullable=False)

  # 设置关系，用于反向访问：projectNode.markers
  projectNode = db.relationship('TreeNode', backref=db.backref('markers', cascade='all, delete-orphan'))

class TreeNode(db.Model):
  __tablename__ = 'projectNode'
  id = db.Column(db.Integer, primary_key=True)
  name = db.Column(db.String(100), nullable=False)
  parentId = db.Column(db.Integer, db.ForeignKey('projectNode.id'), nullable=False)
  color = db.Column(db.String(100), nullable=True, default=None)
  level = db.Column(db.Integer, nullable=False, default=0)

  # 设置关系
  # children = db.relationship('TreeNode', backref='parent', cascade='all, delete-orphan')
  parent = db.relationship('TreeNode', remote_side=[id], backref=db.backref('children', cascade="all, delete-orphan"))

  def to_dict(self, recursive=False):
    data = {
        'id': self.id,
        'name': self.name,
        'parentId': self.parentId,
        'color':self.color,
        'level':self.level
    }
    if recursive:
        data['children'] = [child.to_dict(True) for child in self.children]
    return data

# 生成机器指纹
def get_machine_fingerprint():
    try:
        # 获取主机名
        hostname = socket.gethostname()

        # 动态获取网络接口的 MAC 地址
        mac_address = "00:00:00:00:00:00"  # 默认值，防止无网络接口
        interfaces = psutil.net_if_addrs()
        for interface_name, addrs in interfaces.items():
            # 跳过本地回环接口
            if interface_name.lower() in ['lo', 'loopback']:
                continue
            for addr in addrs:
                if addr.family == psutil.AF_LINK:  # MAC 地址
                    mac_address = addr.address
                    break
            if mac_address != "00:00:00:00:00:00":
                break

        # 获取磁盘序列号（Windows）
        disk_serial = "unknown"
        if platform.system() == "Windows":
            try:
                c = wmi.WMI()
                disk_serial = c.Win32_PhysicalMedia()[0].SerialNumber.strip()
            except Exception:
                pass  # 如果 WMI 失败，使用默认值

        # 获取 CPU 信息
        cpu_info = platform.processor() or "unknown"

        # 备选：使用 UUID 作为补充
        node_uuid = str(uuid.getnode())

        # 组合信息生成哈希
        raw_data = f"{hostname}{mac_address}{disk_serial}{cpu_info}{node_uuid}"
        fingerprint = hashlib.sha256(raw_data.encode()).hexdigest()
        return fingerprint
    except Exception as e:
        raise RuntimeError(f"Failed to generate machine fingerprint: {str(e)}")
    
# 发送指纹到服务端
def send_fingerprint(server_url, fingerprint):
  try:
    response = requests.post(
      f"{server_url}/api/fingerprint",
      json={'fingerprint': fingerprint},
      verify=False # 临时用于测试
    )
    response.raise_for_status()
    return response.json()
  except Exception as e:
    raise RuntimeError(f"Failed to send fingerprint: {str(e)}")

# 验证指纹
def verify_fingerprint(server_url, token, fingerprint):
  try:
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(
      f"{server_url}/api/verify",
      json={'fingerprint': fingerprint},
      headers=headers,
      verify=True
    )
    response.raise_for_status()
    return response.json()
  except Exception as e:
    raise RuntimeError(f"Failed to verify fingerprint: {str(e)}")

def traverse_tree(node, node_list):
  node_list.append(node)
  for child in node.children:
    traverse_tree(child, node_list)

def build_tree(node):
  children = [build_tree(child) for child in node.children]
  return {
    'title': node.name,
    'key': node.id,
    'color': node.color,
    'level': node.level,
    'parentId': node.parentId,
    # 'icon': f"<div className={{styles['{node.color}-dot']}}></div>",
    'children': children if children else []
  }

def get_tree_data(nodes):
  treeData = {'title': '全部', 'key': 0, 'children': []}
  for node in nodes:
    treeData['children'].append(build_tree(node)) 
  return treeData

def get_node_markers(node_id):
  
  node = TreeNode.query.get_or_404(node_id)
  return [marker for marker in node.markers]

with app.app_context():
  db.create_all()
  # 检查是否已存在admin账户
  admin = User.query.filter_by(policeId='000000').first()
  if not admin:
    admin_user = User(
      name='admin',
      policeId='000000',
      privilege='admin/editor/viewer'
    )
    admin_user.set_password('19900115')
    db.session.add(admin_user)
    db.session.commit()

@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response

# === 查询用户 ===
@app.route('/api/users', methods=['GET'])
def get_users():
  try:
    # 获取查询参数
    user_id = request.args.get('id', type=int)
    name = request.args.get('name', type=str)
    policeId = request.args.get('policeId', type=str)

    # 构建查询
    query = User.query
    if user_id:
      query = query.filter_by(id=user_id)
    if name:
      query = query.filter(User.name.ilike(f"%{name}%"))
    if policeId:
      query = query.filter_by(policeId=policeId)

    users = query.all()
    if not users:
      return jsonify({'message': 'No users found matching the criteria'}), 404

    # 返回用户名、警号和权限
    result = [{
      'id': user.id,
      'name': user.name,
      'policeId': user.policeId,
      'privilege': user.privilege.split('/')
    } for user in users]
    return jsonify(result), 200
  except Exception as e:
    return jsonify({'error': str(e)}), 500

# === 登录用户 ===
@app.route('/api/login', methods=['POST'])
def login_users():
  try:
    data = request.get_json()
    userInput = data.get('userInput')
    password = data.get('password')

    if not userInput or not password:
      return jsonify({'error': '需要输入用户名和密码'}), 200
    users = User.query.filter_by(policeId=userInput).all()
    users = users + User.query.filter_by(name=userInput).all()
    if not users:
      return jsonify({'error': '输入的用户名不存在'}), 200
    if len(users) > 1:
      return jsonify({'error': '输入的用户名存在多个，请联系管理员'}), 400
    else:
      user = users[0]
      if not user.check_password(password):
        return jsonify({'error': '密码错误'}), 200
      else:
        return jsonify({
          'id': user.id,
          'name': user.name,
          'policeId': user.policeId,
          'privilege': user.privilege.split('/')
        })
  except Exception as e:
    return jsonify({'error': 'Internal server error'}), 500
  
# === 添加用户 ===
@app.route('/api/users', methods=['POST'])
def add_user():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有admin可以添加用户
    if operatorId is None:
      return jsonify({'error': 'operator ID is required'}), 400
    
    operator = db.session.get(User, operatorId)
    
    ########################
    # 如果admin账户添加账户，则生成机器指纹上传服务器
    if operator.name == 'admin':
      # 生成指纹
      fingerprint = get_machine_fingerprint()
      print(f"Generated fingerprint: {fingerprint}")
      # 发送指纹
      result = send_fingerprint(finger_server_url, fingerprint)
      print(f"Send fingerprint result: {result}")
    #########################
        
    if 'admin' not in operator.privilege.split('/'):
      return jsonify({'error': '没有权限添加账户'}), 201

    name = data.get('name')
    policeId = data.get('policeId')
    password = data.get('password')
    privilege = data.get('privilege', ['viewer'])

    if not all([name, policeId, password]):
      return jsonify({'error': '需要输入用户名，警号和密码'}), 201

    # 检查警号是否已存在
    if User.query.filter_by(policeId=policeId).first():
      return jsonify({'error': '警号已存在'}), 201
    if User.query.filter_by(name=name).first():
      return jsonify({'error': '用户名已存在'}), 201

    new_user = User(
      name=name,
      policeId=policeId,
      privilege='/'.join(privilege) if len(privilege) > 1 else privilege[0]
    )
    new_user.set_password(password)
    db.session.add(new_user)
    db.session.commit()
    return jsonify({'message': 'User added successfully', 'user': new_user.name}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500

# === 修改用户 ===
@app.route('/api/users', methods=['PUT'])
def update_user():
  try:
    data = request.get_json()
    user_id = data.get('id')
    operatorId = data.get('operatorId', None)
    if not user_id:
      return jsonify({'error': 'Operator ID is required'}), 400
    if not operatorId:
      return jsonify({'error': 'User ID is required'}), 400
    
    operator = db.session.get(User, operatorId)
    if 'admin' not in operator.privilege.split('/') and operatorId != user_id:
      return jsonify({'error': '没有权限修改该用户'}), 201
     
    user = db.session.get(User, user_id)
    if not user:
      return jsonify({'error': f'User with ID {user_id} not found'}), 201

    if user.name == 'admin' and operatorId != user_id:
      return jsonify({'error': '不能修改管理员账号'}), 201
  
    # 更新字段
    if 'admin' in operator.privilege.split('/'):
      if 'name' in data:
        user.name = data['name']
      if 'policeId' in data:
        if User.query.filter(User.policeId == data['policeId'], User.id != user_id).first():
          return jsonify({'error': '警号已经存在'}), 201
        user.policeId = data['policeId']
      if 'privilege' in data:
        user.privilege = '/'.join(data['privilege'])
    if 'password' in data:
      user.set_password(data['password'])

    db.session.commit()
    return jsonify({'message': 'User updated successfully', 'user': user.name}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500

# === 删除用户 ===
@app.route('/api/users', methods=['DELETE'])
def delete_user():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有admin可以删除用户
    if operatorId is not None:
      operator = db.session.get(User, operatorId)
      if 'admin' not in operator.privilege.split('/'):
        return jsonify({'error': '没有权限删除该用户'}), 201
      
    user_id = data.get('id')
    if not user_id:
      return jsonify({'error': 'User ID is required'}), 400

    user = db.session.get(User, user_id)
    if not user:
      return jsonify({'error': f'User with ID {user_id} not found'}), 404

    # 防止删除admin账户
    if user.name == 'admin':
      return jsonify({'error': '不能删除管理员账号'}), 201

    db.session.delete(user)
    db.session.commit()
    return jsonify({'message': 'User deleted successfully'}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500

# === 添加节点 ===
@app.route('/api/nodes', methods=['POST'])
def add_node():
  data = request.get_json()
  operatorId = data.get('operatorId', None) # TODO: 只有等级>1可以添加节点
  if operatorId is not None:
    operator = db.session.get(User, operatorId)
    if 'editor' not in operator.privilege.split('/'):
      return jsonify({'error': '没有权限添加项目'}), 201
    
  nodeId = data.get('id', None) # 用于修改节点，不指定则新增节点
  name = data.get('name')
  color = data.get('color')
  parentId = data.get('parent_id', 0)
  level = 1 if parentId == 0 else db.session.get(TreeNode, parentId).level + 1
  if not name:
    return jsonify({'error': 'Node name is required'}), 400
  # if not TreeNode.query.filter_by(id=parentId).first():
  #   return jsonify({'error': 'Parent node not found'}), 404
  
  ############## 暂时修改 !!!!!!!!!!!!!!!!! TODO: 前端需要传入待修改的node的id ######
  # 暂时先用name当id来使用
  query = TreeNode.query.filter_by(
        name=name,
        # parentId=parentId
      )
  nodes = query.all()
  if len(nodes) != 0:
    nodeId = nodes[0].id
  ##################################################################################
  if not nodeId:
    # 查询node是否存在
    existing = TreeNode.query.filter_by(name=name, parentId=parentId).first()
    if existing:
      return jsonify({'message': '项目已经存在', 'node': existing.to_dict()}), 201
    new_node = TreeNode(name=name, parentId=parentId, color=color, level=level)
    db.session.add(new_node)
    db.session.commit()
    return jsonify({'message': 'Node added', 'node': new_node.to_dict()}), 200
  else:
    # 修改node
    node = TreeNode.query.get_or_404(nodeId)
    node.name = name
    node.parentId = parentId
    node.color = color
    # TODO: color变化后，所属的markers的color也要发生变化
    # markers = get_node_markers(nodeId)
    # for marker in markers:
    #   marker.color = color
    db.session.commit()
    return jsonify({'message': 'Node revised', 'node': node.to_dict()}), 200

# # xxxxxx 修改节点 xxxxxx
# @app.route('/api/nodes', methods=['PUT'])
# def update_node():
#   data = request.get_json()
#   operatorId = data.get('operatorId', None)
#     node.level = level
#     db.session.commit()
#     return jsonify({'message': 'Node revised', 'node': node.to_dict()}), 201

# xxxxxx 删除节点及其子节点 xxxxx
@app.route('/api/nodes', methods=['DELETE'])
def delete_node():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有等级>1可以删除节点
    if operatorId is not None:
      operator = db.session.get(User, operatorId)
      if 'editor' not in operator.privilege.split('/'):
        return jsonify({'error': '没有权限删除项目'}), 201
  
    nodeId = data.get('id', None)
    # node = TreeNode.query.get_or_404(nodeId)
    query = TreeNode.query
    if nodeId is not None:
      query = query.filter_by(id=nodeId)
    nodes = query.all()
    if not nodes:
      return jsonify({'message': f'No nodes found with id: {nodeId}'}), 404
    for node in nodes:
      db.session.delete(node)
    db.session.commit()
    return jsonify({'message': 'Node and children deleted'}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500
  
# === 根据条件查询节点 ===
@app.route('/api/nodes/test', methods=['GET'])
def get_nodes_test():
  try:
    nodeId = request.args.get('nodeId', type=int)
    name = request.args.get('name', type=str)
    query = TreeNode.query
    query = query.filter_by(parentId=None)
    if nodeId is not None:
      query = query.filter_by(id=nodeId)
    if name:
      query = query.filter(TreeNode.name.ilike(f"%{name}%"))
    nodes = query.all()
    if not nodes:
      return jsonify({'message': f'No nodes found matching name: {name}'}), 404
    result = [node.to_dict(recursive=False) for node in nodes]
    return jsonify(result), 200
  except Exception as e:
    return jsonify({'error': str(e)}), 500

@app.route('/api/nodes', methods=['GET'])
def get_nodes():
  try:
    query = TreeNode.query
    query = query.filter_by(parentId=0)
    nodes = query.all()
    # if not nodes:
    #   return jsonify({'message': f'No root nodes found matching name: {name}'}), 404
    treeData = get_tree_data(nodes)
    return jsonify(treeData), 200
  except Exception as e:
    return jsonify({'error': str(e)}), 500

# === 查询单个节点及所有子节点 ===
@app.route('/api/nodes/children', methods=['GET'])
def get_node():
  nodeId = request.args.get('nodeId', type=int)
  node = TreeNode.query.get_or_404(nodeId)
  data = jsonify(node.to_dict(recursive=true))
  return data

# === 查询单个节点及所有父节点 ===
@app.route('/api/nodes/parents', methods=['GET'])
def get_parents():
    try:
        # 获取指定节点
        nodeId = request.args.get('nodeId', type=int)
        node = TreeNode.query.get_or_404(nodeId)
        
        # 递归查找所有父节点
        parents = []
        current_node = node
        parents.append(node.to_dict(recursive=False))
        # 遍历父节点
        while current_node.parentId is not None:
            parent = TreeNode.query.get(current_node.parentId)
            if not parent:
                break  # 防止意外的无效 parentId
            parents.append(parent.to_dict(recursive=False))
            current_node = parent
        
        # 按层级从上到下排序（根节点到直接父节点）
        parents.reverse()
        
        return jsonify(parents), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# xxxxxxxxxxxxxx 保存 Markers xxxxxxxxxxxxxxxxx
@app.route('/api/saveMarkers', methods=['POST'])
def save_markers():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有等级>1可以添加Markers
    if operatorId is not None:
      operator = db.session.get(User, operatorId)
      if 'editor' not in operator.privilege.split('/'):
        return jsonify({'error': '没有权限添加标记点'}), 201
  
    markers = data.get('markers', [])
    for marker in markers:

      # 检查Marker的type和secType是否存在，并获取name
      type_id = marker.get('type')
      secType_id = marker.get('secType')
      if not type_id or not secType_id:
        return jsonify({'error': 'Type ID and secType ID is required'}), 400
      type_node = db.session.get(TreeNode, type_id)
      secType_node = db.session.get(TreeNode, secType_id)
      if not type_node or not secType_node:
        return jsonify({'error': f'TreeNode with ID {type_id} or {secType_id} not found'}), 404

      # 查重
      exists = Marker.query.filter_by(
            name=marker['name'],
            latitude=marker['position'][0],
            longitude=marker['position'][1],
            parentId=secType_id).first()
      if exists:
        print(f"Duplicate marker skipped: {marker['name']}")
        continue

      new_marker = Marker(
            name=marker['name'],
            typeId=type_id,
            secTypeId=secType_id,
            latitude=marker['position'][0],
            longitude=marker['position'][1],
            color=type_node.color,
            parentId=secType_id)
      db.session.add(new_marker)
    
    db.session.commit()
    return jsonify({'message': 'Markers saved successfully'}), 200
  except Exception as e:
      db.session.rollback()
      return jsonify({'error': str(e)}), 500

# xxxxxxxx 获取 Markers xxxxxxx
@app.route('/api/markers', methods=['GET'])
def get_markers():
  try:
    # 获取查询参数
    nodeId = request.args.get('nodeId', type=int)
    marker_id = request.args.get('id', type=int)
    name = request.args.get('name', type=str)
    
    nodeIds = []
    if nodeId is not None:
      node = TreeNode.query.get_or_404(nodeId)
      nodeList = []
      traverse_tree(node, nodeList)
      nodeIds = [node.id for node in nodeList]
      
    # 构建查询条件
    query = Marker.query
    if marker_id is not None:
      query = query.filter_by(id=marker_id)
    if name:
      query = query.filter(Marker.name.ilike(f"%{name}%"))  # 模糊匹配，不区分大小写
    if len(nodeIds) > 0:
      query = query.filter(Marker.parentId.in_(nodeIds))
    markers = query.all()

    # 如果没有找到匹配的 Marker，返回明确提示
    # if not markers and name:
    #   return jsonify({'message': f'No markers found matching keyword: {name}'}), 404

    # 序列化返回结果
    result = [{
      'id': m.id,
      'name': m.name,
      'type': m.typeId,
      'secType': m.secTypeId,
      'position': [m.latitude, m.longitude],
      'color': m.color,
      'parentId': m.parentId
    } for m in markers]
    
    ##### 临时!!!! TODO: name和key都存 ####
    # result = []
    # for m in markers:
    #   query = TreeNode.query.filter_by(name=m.type)
    #   node = query.all()[0]
    #   result.append({
    #     'id': m.id,
    #     'name': m.name,
    #     'type': node.id,
    #     'secType': m.parentId,
    #     'position': [m.latitude, m.longitude],
    #     'color': m.color,
    #     'parentId': m.parentId
    #   })
    ######################################
    
    return jsonify(result), 200
  except Exception as e:
    return jsonify({'error': str(e)}), 500

# xxxxx 删除 xxxxx
@app.route('/api/markers', methods=['DELETE'])
def delete_marker():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有等级>1可以删除节点
    if operatorId is not None:
      operator = db.session.get(User, operatorId)
      if 'editor' not in operator.privilege.split('/'):
        return jsonify({'error': '没有权限删除标记点'}), 201
    
    marker = data.get('marker', None)
    if not marker:
      return jsonify({'error': 'marker is required'}), 400
    db.session.delete(db.session.get(Marker, marker['id']))
    db.session.commit()
    return jsonify({'message': 'Marker deleted successfully'}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500

# === 更新 Marker ===
@app.route('/api/markers', methods=['PUT'])
def update_marker():
  try:
    data = request.get_json()
    operatorId = data.get('operatorId', None) # TODO: 只有等级>1可以修改节点
    if operatorId is not None:
      operator = db.session.get(User, operatorId)
      if 'editor' not in operator.privilege.split('/'):
        return jsonify({'error': '没有权限编辑标记点'}), 201
    
    # 获取 Marker
    markers = data.get('markers', None)
    if markers is not None:
      for markerData in markers:
        markerId = markerData['id']
        marker = db.session.get(Marker, markerId)
        if not marker:
          return jsonify({'error': f'Marker with ID {markerId} not found'}), 404
        
        # 检查Marker的type和secType是否存在，并获取name
        type_id = markerData.get('type')
        secType_id = markerData.get('secType')
        if not type_id or not secType_id:
          return jsonify({'error': 'Type ID and secType ID is required'}), 400
        type_node = db.session.get(TreeNode, type_id)
        secType_node = db.session.get(TreeNode, secType_id)
        if not type_node or not secType_node:
          return jsonify({'error': f'TreeNode with ID {type_id} or {secType_id} not found'}), 404
      
        # 修改字段
        marker.name =markerData['name']
        marker.typeId = type_id
        marker.secTypeId = secType_id
        marker.latitude = markerData['position'][0]
        marker.longitude = markerData['position'][1]
        marker.color = type_node.color
        marker.parentId = secType_id

    db.session.commit()
    return jsonify({'message': 'Marker updated successfully'}), 200
  except Exception as e:
    db.session.rollback()
    return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=30010)