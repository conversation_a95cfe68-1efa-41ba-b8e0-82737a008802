import React, { useState } from 'react';
import Login from './Login';
import MapPage from './MapPage.js';
import { TRUE, sassFalse } from 'sass';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLogin = () => {
    setIsLoggedIn(v=>!v);
  };

  return (
    <div style={{height:'100vh',overflow:'hidden'}}>
      {isLoggedIn ? <>
      <MapPage logOut={handleLogin}/>
      </>: <Login onLogin={handleLogin} />}
    </div>
  );
}

export default App;