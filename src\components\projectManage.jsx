import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from '../index.module.scss';
import { Modal, Input, Tree, Button, Form, Select, Tag, message } from 'antd';
import { PlusOutlined, ContainerOutlined } from '@ant-design/icons';
// import NodeTree from './nodeTree';
import APIS from '../services';

const { Search } = Input;

const options = [{ label: '红色', value: '#ff0004' }, { label: '蓝色', value: '#01C2FF' }, { label: '绿色', value: '#32c717' }, { label: '橘色', value: '#FF8838' }];

const ProjectManage = ({ visible, handleCancel, _treeData, changeTree, onRefresh }) => {
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [firstOpts, setFirstOpts] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const [showModal, setModal] = useState(false)
  const [modalType, setModalType] = useState('1')
  const [form] = Form.useForm();

  useEffect(() => {
    const _tmp = []
    if (!_treeData.length) return;
    _treeData[0]?.children.map(v => {
      _tmp.push({
        label: v.title,
        value: v.key
      })
    })
    setFirstOpts(_tmp)
    setTreeData(_treeData)
  }, [_treeData])

  const handleModal = (type) => {
    setModal(true)
    setModalType(type)
    setSelectedKeys([])
  }

  // 新增项目
  const handleConfirm = async (values) => {
    const { color, firstType, name } = values
    const res = await APIS.addProjects({
      ...values,
      parent_id: modalType === '1' ? 0 : firstType,
      color,
      name,
      id: selectedKeys[0]
    });
    if (!res.error) {
      message.success(`${selectedKeys[0] ? '编辑' : '新增'}项目成功！`)
      form.resetFields();
      setModal(false);
      onRefresh();
    } else {
      message.error(res.error)
    }

  }

  const tagRender = props => {
    const { label, value, closable, onClose } = props;
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    return (
      <Tag
        color={value}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{ marginInlineEnd: 4 }}
      >
        {label}
      </Tag>
    );
  };

  return (
    <Modal
      width={600}
      centered
      title='项目管理'
      open={visible}
      onCancel={handleCancel}
      footer={null}
      maskClosable
    >
      {/* <Search style={{ margin: '12px 0' }} placeholder="一级/二级" onChange={onChange} /> */}
      <Button color="primary" variant="outlined" style={{ marginRight: 10 }} size='small' onClick={() => handleModal('1')}><PlusOutlined /> 新增一级</Button>
      <Button color="primary" variant="outlined" style={{ marginBottom: 10 }} onClick={() => handleModal('2')} size='small'><PlusOutlined /> 新增二级</Button>
      <Tree
        className={styles['tree']}
        defaultExpandedKeys={[0, 1, 2, 3]}
        showIcon={true}
        showLine={true}
        treeData={treeData}
        onSelect={(selectedKeys, e) => {
          setSelectedKeys([e.node?.key])
          setModalType(e.node?.level + '')
          form.setFieldsValue({
            id: e.node?.key,
            name: e.node?.title,
            color: e.node?.color,
            firstType: e.node?.parentId,
          })
          setModal(true)
        }}
      />
      <Modal
        width={350}
        centered
        title={`${modalType === '1' ? '一' : '二'}级项目`}
        open={showModal}
        onCancel={() => {
          setModal(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          style={{ marginTop: 20 }}
          onFinish={(values) => handleConfirm(values)}
        >
          <Form.Item hidden={modalType === '1'} label='一级项目' name="firstType" rules={[{ required: false, message: '一级项目必填' }]}>
            <Select options={firstOpts} placeholder='请选择一级项目' />
          </Form.Item>
          <Form.Item label='项目名称' name="name" rules={[{ required: false, message: '项目名称必填' }]}>
            <Input placeholder='请输入项目名称' />
          </Form.Item>
          <Form.Item hidden={modalType === '2'} label='颜色类别' name="color" rules={[{ required: false, message: '颜色等级必填' }]}>
            <Select placeholder='请选择颜色等级' >
              {options.map(option => (
                <Option key={option.value} value={option.value}>
                  <Tag color={option.value}>{option.label}</Tag>
                </Option>
              ))}
            </Select>
            {/* <Select mode="tags" maxCount={1} placeholder='请选择颜色等级' tagRender={tagRender} options={options} /> */}
          </Form.Item>
          <Form.Item label={null}>
            <Button type="primary" htmlType="submit">
              确认
            </Button>
            {selectedKeys.length > 0 && <Button style={{ marginLeft: 5 }} type="default" onClick={async () => {
              const res = await APIS.rmProjects({
                id: selectedKeys[0]
              });
              if (!res.error) {
                message.success('删除项目成功！')
                form.resetFields();
                setModal(false);
                onRefresh();
              } else {
                message.error(res.error)
              }
            }}>
              删除
            </Button>}
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default ProjectManage;
