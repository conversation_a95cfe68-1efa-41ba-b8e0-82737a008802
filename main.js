const { spawn } = require('child_process');
const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    // 设置窗口图标
    icon: path.join(__dirname, 'public', 'icons', process.platform === 'win32' ? 'police.ico' : process.platform === 'darwin' ? 'police.icns' : 'police.png'),
    autoHideMenuBar: true
  });

  // 在开发模式下加载本地 React 开发服务器
  if (process.env.NODE_ENV === 'development') {
    win.loadURL('http://localhost:3000');
    win.webContents.openDevTools();
  } else {
    // 在生产模式下加载构建后的静态文件
    win.loadFile(path.join(__dirname, 'build', 'index.html'));
  }
}

app.whenReady().then(() => {
  createWindow();

  const exePath = app.isPackaged
    ? path.join(process.resourcesPath, 'sql.exe')
    : path.join(__dirname, 'dist', 'sql.exe');

  console.log('Launching backend from:', exePath);

  pythonProcess = spawn(exePath);

  pythonProcess.stdout.on('data', data => {
    console.log(`Backend: ${data}`);
  });

  pythonProcess.stderr.on('data', data => {
    console.error(`Backend Error: ${data}`);
  });

  pythonProcess.on('close', code => {
    console.log(`Backend exited with code ${code}`);
  });
});

app.on('window-all-closed', () => {
  if (pythonProcess) pythonProcess.kill();
  app.quit();
});