import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from '../index.module.scss';
import { Modal, message, Table, Button, Form, Input, Checkbox, Popconfirm } from 'antd';
import { UserOutlined, KeyOutlined } from '@ant-design/icons';
import APIS from '../services';

const Password = ({ visible, handleCancel, userInfo }) => {
    const [form] = Form.useForm();

    useEffect(() => {
        form.setFieldsValue({
            name: userInfo.name
        })
    }, [userInfo])

    const handleConfirm = async (values) => {
        const { username, password, passwordConfirm } = values
        if (passwordConfirm !== password) {
            return message.success('两次密码输入不一致')
        }
        const res = await APIS.editAccount({
            id: userInfo.id,
            password: password,
            operatorId: userInfo.id,
        });
        if (res.error) {
            message.error(res.error)
        } else {
            message.success('密码修改成功！')
            handleCancel()
        }
    }

    return (
        <Modal
            width={350}
            centered
            title='修改密码'
            open={visible}
            onCancel={handleCancel}
            footer={null}
        >
            <Form
                form={form}
                style={{ marginTop: 20 }}
                onFinish={(values) => handleConfirm(values)}
            >
                <Form.Item label={null} name="name" rules={[{ required: false, message: '请输入账号' }]}>
                    <Input disabled addonBefore={<UserOutlined />} placeholder='请输入账号' />
                </Form.Item>
                <Form.Item label={null} name="password" rules={[{ required: false, message: '请输入密码' }]}>
                    <Input addonBefore={<KeyOutlined />} placeholder='请输入密码' />
                </Form.Item>
                <Form.Item label={null} name="passwordConfirm" rules={[{ required: false, message: '请输入密码' }]}>
                    <Input addonBefore={<KeyOutlined />} placeholder='请再次确认密码' />
                </Form.Item>
                <Form.Item label={null}>
                    <Button type="primary" htmlType="submit">
                        确认
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Password;