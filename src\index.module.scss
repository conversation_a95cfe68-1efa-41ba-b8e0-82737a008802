@mixin dot {
    border-radius: 50%;
    width: 16px;
    height: 16px;
}

@mixin card-style {
    border-radius: 4px;
    background: #fff;
    padding: 12px;
    box-shadow: 0 4px 10px 0 rgba(32, 60, 153, .1);
}

.page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgb(18 26 33);

    .header-text {
        font-size: 18px;
        padding: 6px 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 58px;
        background-image: url("../public/bg2.png"),url("../public/bgt.webp");
        background-size: 100% 100%, 65% 80%;
        background-position: center, center;
        background-repeat: repeat, no-repeat;
    }

    .hover-button{
        color: #6ab7d2;
        font-weight: bold;
        transition: transform 0.3s ease;
    }

    .hover-button:hover{
        color: #6ab7d2 !important;
        transform: scale(1.1) translateZ(50px);
    }

    .header-text-container{
        flex: 1;
        height: 100%;
        background-size: 100% 100%;
        display: flex;
        align-items: start;
        justify-content: center;
        overflow: unset;
        text-overflow: unset;
        white-space: unset;
        color: rgb(255, 255, 255);
        background-clip: unset;
        -webkit-text-fill-color: initial;
        font-size: 28px;
    }

    .header-top-buttons {
        @include card-style();
        margin: 12px;
        background-size: 100% 200%;
        background-position: center bottom;
        background-repeat: no-repeat;

        button {
            border: none;
            border-radius: 0;
            box-shadow: none;
            width: calc((100% - 5px) / 6);
            background: none;
        }

        button:not(:last-child) {
            border-right: 1px solid #edf2fa;
        }

        button:hover {
            background: transparent !important;
            border-color: #edf2fa !important;
        }
    }

    .main-container {
        flex: 1;
        padding: 0 12px;
        height: 100%;
        width: 100%;
        position: relative;
    }

    div[class~=map-container] {
        display: flex;
        align-items: center;
        height: 100%;
    }

    .map-title {
        position: absolute;
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 999;
        background: rgba(0, 123, 255, 0.6);
        color: #fff;
        font-weight: 500;
        border-radius: 16px;
        font-size: 30px;
        padding: 5px 10px;
    }

    .btn-style{
        width: 25%;
        display: flex;
        justify-content: space-between;
    }

    .marker-menu {
        background-image: url("../public/leftSideMenu.webp");
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
        top: 10px;
        left: 0%;
        right: 0;
        z-index: 999;
        width: 300px;
        height: 65%;
        padding: 5px 12px;

        div[class~=ant-select-selector],span[class~=ant-input-outlined],input{
            background: #0F2B3F !important;
            color: #fff !important; 
        }
    }

    .marker-menu-content {
        max-height: 85%;
        overflow-y: scroll;
        scrollbar-width: none;
        -ms-overflow-style: none;
        background: #0F2B3F;
        padding-left: 5px;
        margin-top: 5px;
    }

    .marker-menu-content::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari, Opera*/
    }

    .marker-search{
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .marker-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-bottom: 2px;
        color: rgb(65 156 214);
        font-weight: 500;

        .divide-line {
            display: inline-block;
            width: 0;
            height: 12px;
            border: 2px solid rgb(79, 150, 249);
            margin-right: 4px;
        }
    }

    .marker-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 16px;
        margin: 10px 0;
        font-size: 14px;
        color: #454545;

        :not(.marker-item-tag):hover {
            cursor: pointer;
            color: #1677ff;
        }
    }

    .marker-item-tag {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 90px;
    }

    .marker-item-second-tag {
        @extend .marker-item-tag;
        max-width: 65px;
    }

    .marker-item-event {
        @extend .marker-item-tag;
        color: #A8D6FF;
        font-weight: 500;
    }

    .side-menu {
        // @include card-style();
        width: 18%;
        position: absolute;
        right: 12px;
        top: 20px;
        bottom: 20px;
        z-index: 999;
        border-radius: 8px;
        background: rgb(18 26 33);

        .side-menu-icon {
            background: rgb(18 26 33);
            padding: 8px 5px;
            border-radius: 6px 0 0 6px;
            position: absolute;
            left: -26px;
            color: #1677ff;

            :hover {
                cursor: pointer;
                color: #1677ff;
            }
        }
    }


    .side-menu-title {
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        background: #20426a;
        padding: 5px 8px;
        margin: -12px -12px 10px -12px;
        border-left: 5px solid #33afff;
    }

    .side-menu-text-card {
        font-size: 14px;
        color: #29F1FA;
        padding: 12px;
        background-image: url("../public/cardBg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        span {
            color: #fff;
            line-height: 16px;
        }
    }

    .side-tree-div {
        overflow-y: scroll;
        scrollbar-width: none;
        -ms-overflow-style: none;
        height: calc(100% - 35px);
        flex: 1;
    }

    .side-tree-div::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari, Opera*/
    }

    .address-icon {
        width: 24px;
        height: 24px;
        margin-right: 5px;
    }

    div[class~=ant-tree] {
        background: transparent !important;
        color: #fff !important;
    }
   
}

.user-info {
    background: #fff;
    padding: 10px 15px;
    box-shadow: 0 4px 10px 0 rgba(32, 60, 153, .1);
    border-radius: 0 0 8px 8px;
    margin-right: -12px;

    div {
        line-height: 28px;
        color: #1D2129;
    }

    .menu-icon:hover {
        color: #1677ff;
        cursor: pointer;
    }
}

.red-dot {
    @include dot();
    background: red;
}

.yellow-dot {
    @include dot();
    background: rgb(18, 96, 179);
}

.green-dot {
    @include dot();
    background: rgb(218, 222, 20);
}

.dot {
    @include dot();
}

.tree span {
    line-height: 16px;
}

.avator-bg{
    display: inline-block;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: rgb(0, 123, 255);
    display: flex;
    align-items: center;
    justify-content: center;
}

span[class~=ant-tree-node-selected] {
    background: transparent !important;
    // color: red !important;
}

.add-marker-btn{
    border: 1px solid rgb(65 156 214);
    background: radial-gradient(ellipse at center, transparent 70%, rgb(65 156 214) 100%);
    color: #A8D6FF;
    border-radius: 50px;
    font-weight: 500;
}
.add-marker-btn:hover{
    border: 1px solid rgb(65 156 214) !important;
    background: rgb(65 156 214) !important;
    color: #fff !important;
}

.context-menu {
    background-image: url("../public/markerCard.webp");
    background-size: 100% 100%;
    background-position: center;
    background-repeat:  no-repeat;
    position: absolute;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    color: #A8D6FF !important;
    font-weight: 500;
    div[class~=ant-select-selector],span[class~=ant-input-outlined],input{
        background: #0F2B3F !important;
        color: #fff !important; 
    }
}

