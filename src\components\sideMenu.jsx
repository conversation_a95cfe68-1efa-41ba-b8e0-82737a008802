import React, { use, useCallback, useEffect, useRef, useState } from 'react';
import styles from '../index.module.scss';
import { Input, Tree } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import NodeTree from './nodeTree';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';

const { Search } = Input;

const SideMenu = ({ stats, changeSelect, onFilter, _treeData, markers }) => {
    const [isShow, setIsShow] = useState(true)

    const handleSelect = (key, e) => {
        changeSelect(e)
    }


    return (
        <div className={styles['side-menu']} style={{ width: !isShow && 0 }}>
            <div className={styles['side-menu-icon']} onClick={() => setIsShow(v => !v)}>
                {isShow ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </div>
            {isShow && <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <div className={styles['side-menu-text-card']}>
                    <div className={styles['side-menu-title']}>基本信息</div>
                    <div style={{ margin: '4px 0' }}>当前位置：<span>鲁山县</span></div>
                    <div>纬度：<span>{stats.latitude}</span></div>
                    <div style={{ margin: '4px 0' }}>经度：<span>{stats.longitude}</span></div>
                    <div>标记数量：<span>{markers?.length ?? 0}</span></div>
                </div>

                <div className={styles['side-menu-text-card']} style={{ marginTop: 16, flex: 1, overflow: 'hidden' }}>
                    <div className={styles['side-menu-title']}>筛选</div>
                    {/* <Search style={{ margin: '12px 0' }} placeholder="Search" onChange={onChange} /> */}
                    <div className={styles['side-tree-div']}>
                        <Tree
                            defaultExpandAll={true}
                            // defaultExpandedKeys={[0, 1, 2, 3]}
                            showIcon={true}
                            showLine={true}
                            treeData={_treeData}
                            onSelect={(v, obj) => {
                                onFilter(obj.node)
                            }}
                        />
                    </div>
                    {/* <NodeTree _treeData={_treeData} onChange={v => onFilter(v)} /> */}
                    {/* <Tree 
                    // defaultExpandedKeys={[0, 1, 2, 3]}
                    showIcon={true}
                    showLine={true}
                    treeData={JSON.parse(localStorage.getItem('treeData'))}
                    onSelect={(selectedKeys,e)=>handleSelect(selectedKeys,e)}
                /> */}
                </div>
            </div>}

        </div>
    );
};

export default SideMenu;
