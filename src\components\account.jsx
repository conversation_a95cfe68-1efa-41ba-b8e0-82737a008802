import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from '../index.module.scss';
import { Modal, Tag, Table, Button, Form, Input, Checkbox, Popconfirm, message } from 'antd';
import { QuestionCircleOutlined, UserAddOutlined } from '@ant-design/icons';
import APIS from '../services';

const options = [
    { label: '管理', value: 'admin' },
    { label: '编辑', value: 'editor' },
    { label: '查看', value: 'viewer' },
];

const Account = ({ visable, handleCancel }) => {
    useEffect(() => {
        getAccountList()
    }, [])

    const [form] = Form.useForm();

    const columns = [
        {
            title: '序号',
            dataIndex: 'id',
            key: 'id',
            width: 60,
            align: 'center'
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            align: 'center'
        },
        {
            title: '警号',
            dataIndex: 'policeId',
            key: 'policeId',
            align: 'center'
        },
        // {
        //     title: '创建时间',
        //     dataIndex: 'time',
        //     key: 'time',
        //     align: 'center'
        // },
        {
            title: '权限',
            dataIndex: 'privilege',
            key: 'privilege',
            render: (_, { privilege }) => (
                <>
                    {privilege?.map(tag => {
                        let color = tag === 'admin' ? 'geekblue' : (tag === 'editor' ? 'orange' : 'green');
                        return (
                            <Tag color={color} key={tag}>
                                {tag === 'admin' ? '管理' : (tag === 'editor' ? '编辑' : '查看')}
                            </Tag>
                        );
                    })}
                </>
            ),
        },
        {
            title: '操作',
            key: 'operation',
            lock: 'right',
            render: (_, record) => (
                <>
                    <Button color="primary" variant="text" size='small' onClick={() => handelShowAddOrEdit('edit', record)}>编辑</Button>
                    <Popconfirm
                        title="是否删除该用户？"
                        onConfirm={() => handelRemove(record)}
                        icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button color="primary" variant="text" size='small'>删除</Button>
                    </Popconfirm>
                </>

            ),
        },
    ];

    const [addOrEditVisable, setAddOrEditVisible] = useState(false)
    const [newModaltype, setNewModaltype] = useState('')
    const [dataList, setDataList] = useState([])
    const [editId, setEditId] = useState('')

    const handelShowAddOrEdit = (type, record) => {
        type && setNewModaltype(type)
        if (type === 'edit') {
            setEditId(record.id)
            form.setFieldsValue({
                ...record
            })
        }
        setAddOrEditVisible(v => !v)
    }

    const handelRemove = async (record) => {
        const data = await APIS.rmAccount({
            id: record.id
        })
        if (!data.error) {
            message.success('删除账户成功！')
            getAccountList()
        } else {
            message.error(data.error)
        }
    }

    const handleConfirm = async (values) => {
        const data = await APIS[newModaltype === 'edit' ? 'editAccount' : 'addAccounts']({
            ...values,
            ...(newModaltype === 'edit' ? { id: editId } : {}),
            operatorId: JSON.parse(localStorage.getItem('_USER')).id,
        })
        if (!data.error) {
            message.success('新增账户成功！');
            setAddOrEditVisible(false)
            form.resetFields();
            getAccountList()
        } else {
            message.error(data.error);
        }
    }

    const getAccountList = async () => {
        const data = await APIS.getAccountList({})
        setDataList(data)
    }

    return (
        <Modal
            width={1000}
            centered
            title="账号管理"
            open={visable}
            onCancel={handleCancel}
            footer={null}
        >
            <Button color="primary" variant="outlined" style={{ marginBottom: 10 }} onClick={() => handelShowAddOrEdit('add')} size='small'><UserAddOutlined />新增</Button>
            <Table dataSource={dataList} columns={columns} size="small" />
            <Modal
                width={500}
                centered
                title={`${newModaltype === 'edit' ? '编辑' : '新增账号'}`}
                open={addOrEditVisable}
                onCancel={() => {
                    handelShowAddOrEdit()
                    form.resetFields();
                }}
                footer={null}
            >
                <Form
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 16 }}
                    form={form}
                    onFinish={(values) => handleConfirm(values)}
                >
                    <Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入姓名' }]}>
                        <Input placeholder='请输入姓名' disabled={newModaltype === 'edit'} />
                    </Form.Item>
                    <Form.Item label="警号" name="policeId" rules={[{ required: true, message: '请输入警号' }]}>
                        <Input placeholder='请输入警号' />
                    </Form.Item>
                    <Form.Item label="初始密码" name="password" rules={[{ required: true, message: '请输入初始密码' }]}>
                        <Input placeholder='请输入初始密码' />
                    </Form.Item>
                    <Form.Item label="权限" name="privilege" >
                        <Checkbox.Group
                            options={options}
                        />
                    </Form.Item>
                    <Form.Item label={null}>
                        <Button type="primary" htmlType="submit">
                            确认
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </Modal>
    );
};

export default Account;