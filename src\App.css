.app {
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.buttons button {
  margin-left: 10px;
  padding: 5px 10px;
  cursor: pointer;
}

.main-content {
  display: flex;
}

.map-container {
  flex: 3;
  position: relative;
}

.map-title {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 123, 255, 0.8);
  color: white;
  padding: 5px 20px;
  font-size: 30px;
  font-weight: bold;
  z-index: 1000;
}

.sidebar {
  flex: 1;
  padding: 20px;
  background-color: #f9f9f9;
  border-left: 1px solid #ddd;
}

.stats,
.legend {
  margin-bottom: 20px;
}

.stats h3,
.legend h3 {
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.dot {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.dot.red {
  background-color: red;
}

.dot.blue {
  background-color: blue;
}

.dot.yellow {
  background-color: yellow;
}

/* 右键菜单样式 */
.context-menu {
  background-image: url("../public/markerCard.webp");
  background-size: 100% 100%;
  background-position: center;
  background-repeat:  no-repeat;
  position: absolute;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  color: #A8D6FF !important;
  font-weight: 500;
}

.context-menu h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.context-menu select {
  margin-right: 10px;
  padding: 5px;
  width: 100%;
}

.context-menu button {
  padding: 5px 10px;
  cursor: pointer;
  margin-top: 5px;
  width: 100%;
}